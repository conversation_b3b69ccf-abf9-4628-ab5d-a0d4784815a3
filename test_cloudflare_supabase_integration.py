#!/usr/bin/env python3
"""
Test Cloudflare + Supabase Integration

This script tests the complete integration of Cloudflare Secrets Store with Supabase.
"""

import asyncio
import os
from core.config.settings import get_settings
from core.config.supabase_secrets import get_supabase_secrets_loader
from core.config.secret_providers import SecretConfigProvider

async def test_cloudflare_connection():
    """Test Cloudflare Secrets Store connection"""
    print("1️⃣ Testing Cloudflare Connection...")
    
    try:
        provider = SecretConfigProvider(provider_type="auto")
        info = provider.get_provider_info()
        
        print(f"   ✅ Provider: {info['provider_class']}")
        print(f"   ✅ Type: {info['provider_type']}")
        
        # Test health check
        healthy = await provider.health_check()
        if healthy:
            print("   ✅ Health check: PASSED")
            return True
        else:
            print("   ❌ Health check: FAILED")
            return False
            
    except Exception as e:
        print(f"   ❌ Connection failed: {e}")
        return False

async def test_supabase_secrets_loading():
    """Test loading Supabase secrets"""
    print("\n2️⃣ Testing Supabase Secrets Loading...")
    
    try:
        # Test loading from secrets store
        loader = get_supabase_secrets_loader(use_secrets_store=True)
        credentials = await loader.load_credentials()
        
        print(f"   ✅ URL: {credentials.url}")
        print(f"   ✅ Anon Key: {credentials.anon_key[:20]}..." if credentials.anon_key else "   ❌ Anon Key: Missing")
        print(f"   ✅ Service Key: {credentials.service_role_key[:20]}..." if credentials.service_role_key else "   ❌ Service Key: Missing")
        print(f"   ✅ DB URI: {credentials.db_uri[:30]}..." if credentials.db_uri else "   ❌ DB URI: Missing")
        print(f"   ✅ Complete: {credentials.is_complete}")
        
        if credentials.project_id:
            print(f"   ✅ Project ID: {credentials.project_id}")
            print(f"   ✅ Dashboard: {credentials.dashboard_url}")
        
        return credentials.is_complete
        
    except Exception as e:
        print(f"   ❌ Loading failed: {e}")
        return False

async def test_settings_integration():
    """Test settings integration with secrets"""
    print("\n3️⃣ Testing Settings Integration...")
    
    try:
        settings = get_settings()
        
        # Load secrets into settings
        success = await settings.load_supabase_secrets()
        
        if success:
            print("   ✅ Secrets loaded into settings")
            print(f"   ✅ Supabase enabled: {settings.is_supabase_enabled()}")
            print(f"   ✅ Database URL: {settings.get_database_url()[:50]}...")
            print(f"   ✅ Dashboard: {settings.get_supabase_dashboard_url()}")
            return True
        else:
            print("   ❌ Failed to load secrets into settings")
            return False
            
    except Exception as e:
        print(f"   ❌ Settings integration failed: {e}")
        return False

async def test_application_startup():
    """Test application startup with secrets"""
    print("\n4️⃣ Testing Application Startup...")
    
    try:
        from core.application.factory import create_application
        
        settings = get_settings()
        await settings.load_supabase_secrets()
        
        app = create_application(settings)
        
        print("   ✅ Application created successfully")
        print(f"   ✅ Title: {app.title}")
        print(f"   ✅ Version: {app.version}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Application startup failed: {e}")
        return False

async def test_supabase_client():
    """Test Supabase client with loaded secrets"""
    print("\n5️⃣ Testing Supabase Client...")
    
    try:
        from core.infrastructure.simple_supabase import SimpleSupabaseClient
        
        # Load settings with secrets
        settings = get_settings()
        await settings.load_supabase_secrets()
        
        if not settings.is_supabase_enabled():
            print("   ❌ Supabase not enabled after loading secrets")
            return False
        
        # Create and test client
        client = SimpleSupabaseClient(settings.supabase)
        await client.initialize()
        
        print(f"   ✅ Client initialized: {client._initialized}")
        print(f"   ✅ Client available: {client.is_available}")
        
        # Test health check
        health = await client.health_check()
        print(f"   ✅ Health check: {health['database_status']}")
        
        await client.dispose()
        return True
        
    except Exception as e:
        print(f"   ❌ Supabase client test failed: {e}")
        return False

async def test_fallback_behavior():
    """Test fallback to environment variables"""
    print("\n6️⃣ Testing Fallback Behavior...")
    
    try:
        # Test with secrets store disabled
        loader = get_supabase_secrets_loader(use_secrets_store=False)
        credentials = await loader.load_credentials()
        
        print("   ✅ Fallback to environment variables works")
        print(f"   ✅ URL from env: {credentials.url is not None}")
        print(f"   ✅ Complete from env: {credentials.is_complete}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Fallback test failed: {e}")
        return False

async def show_configuration_summary():
    """Show current configuration summary"""
    print("\n📋 Configuration Summary:")
    
    # Cloudflare info
    account_id = os.getenv('CLOUDFLARE_ACCOUNT_ID')
    api_token = os.getenv('CLOUDFLARE_API_TOKEN')
    
    print(f"   • Cloudflare Account: {account_id[:8]}...{account_id[-8:] if account_id else 'Not set'}")
    print(f"   • API Token: {'Set' if api_token else 'Not set'}")
    
    # Supabase info
    try:
        loader = get_supabase_secrets_loader()
        info = loader.get_provider_info()
        
        print(f"   • Secrets Store: {'Enabled' if info['use_secrets_store'] else 'Disabled'}")
        print(f"   • Secrets Prefix: {info['secrets_prefix']}")
        print(f"   • Provider Available: {info['provider_available']}")
        
    except Exception as e:
        print(f"   • Configuration error: {e}")

async def main():
    """Run all integration tests"""
    print("🔐 Cloudflare + Supabase Integration Test")
    print("=" * 50)
    
    # Show configuration
    await show_configuration_summary()
    
    # Run tests
    tests = [
        ("Cloudflare Connection", test_cloudflare_connection),
        ("Supabase Secrets Loading", test_supabase_secrets_loading),
        ("Settings Integration", test_settings_integration),
        ("Application Startup", test_application_startup),
        ("Supabase Client", test_supabase_client),
        ("Fallback Behavior", test_fallback_behavior),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ Test error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your Cloudflare + Supabase integration is working perfectly!")
        print("\n🚀 Ready for production deployment with secure secret management!")
    else:
        print("\n⚠️  Some tests failed. Please check the configuration and try again.")
        print("\n💡 Troubleshooting:")
        print("   1. Ensure Cloudflare credentials are correct")
        print("   2. Verify Supabase secrets are stored in Cloudflare")
        print("   3. Check network connectivity")

if __name__ == "__main__":
    asyncio.run(main())
