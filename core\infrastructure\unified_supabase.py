"""
Unified Supabase Client

This module provides a single, comprehensive Supabase client that replaces:
- Custom JWT authentication
- Database user stores
- Secret management complexity
- Multiple authentication providers

Everything is handled through Supabase's native services.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timezone

from supabase import create_client, Client
from gotrue import User as SupabaseUser
from postgrest import APIResponse

from core.config.settings import SupabaseSettings
from core.domain.models import User, Role, UserStatus
from core.domain.interfaces import IUserStore, IRoleStore, ISecurityProvider

logger = logging.getLogger(__name__)


class UnifiedSupabaseClient:
    """
    Unified Supabase client that provides all services:
    - Authentication (replaces JWT + custom auth)
    - Database operations (replaces SQLAlchemy stores)
    - Real-time subscriptions
    - File storage
    - Row Level Security
    """

    def __init__(self, settings: SupabaseSettings):
        self.settings = settings
        self._client: Optional[Client] = None
        self._service_client: Optional[Client] = None  # For admin operations
        self._initialized = False
        
        # Service availability flags
        self.auth_available = False
        self.database_available = False
        self.storage_available = False
        self.realtime_available = False

    async def initialize(self) -> None:
        """Initialize the unified Supabase client"""
        if self._initialized:
            return

        try:
            if not self.settings.supabase_url or not self.settings.supabase_anon_key:
                raise ValueError("Supabase URL and anon key are required")

            # Create client for user operations (with anon key)
            self._client = create_client(
                self.settings.supabase_url,
                self.settings.supabase_anon_key
            )

            # Create service client for admin operations (with service role key)
            if self.settings.supabase_service_role_key:
                self._service_client = create_client(
                    self.settings.supabase_url,
                    self.settings.supabase_service_role_key
                )

            # Test services
            await self._test_services()
            
            self._initialized = True
            logger.info("✅ Unified Supabase client initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            raise

    async def _test_services(self) -> None:
        """Test availability of Supabase services"""
        
        # Test database
        try:
            # Simple query to test database connectivity
            response = self._client.table("_realtime_schema").select("*").limit(1).execute()
            self.database_available = True
            logger.info("✅ Database service available")
        except Exception as e:
            logger.warning(f"Database service test failed: {e}")

        # Test auth
        if self.settings.enable_auth:
            try:
                # Test auth service
                self._client.auth.get_user()  # This will work even if no user is logged in
                self.auth_available = True
                logger.info("✅ Auth service available")
            except Exception as e:
                logger.warning(f"Auth service test failed: {e}")

        # Test storage
        if self.settings.enable_storage:
            try:
                # Test storage service
                buckets = self._client.storage.list_buckets()
                self.storage_available = True
                logger.info("✅ Storage service available")
            except Exception as e:
                logger.warning(f"Storage service test failed: {e}")

        # Test realtime
        if self.settings.enable_realtime:
            try:
                # Realtime is available if we can create a client
                self.realtime_available = True
                logger.info("✅ Realtime service available")
            except Exception as e:
                logger.warning(f"Realtime service test failed: {e}")

    # Authentication Methods (replaces ISecurityProvider)
    
    async def sign_up(self, email: str, password: str, user_metadata: Optional[Dict] = None) -> Dict[str, Any]:
        """Sign up a new user with Supabase Auth"""
        if not self.auth_available:
            raise RuntimeError("Auth service not available")

        try:
            response = self._client.auth.sign_up({
                "email": email,
                "password": password,
                "options": {"data": user_metadata or {}}
            })
            
            if response.user:
                logger.info(f"User signed up successfully: {email}")
                return {
                    "success": True,
                    "user": response.user,
                    "session": response.session
                }
            else:
                return {"success": False, "error": "Sign up failed"}

        except Exception as e:
            logger.error(f"Sign up failed for {email}: {e}")
            return {"success": False, "error": str(e)}

    async def sign_in(self, email: str, password: str) -> Dict[str, Any]:
        """Sign in user with Supabase Auth"""
        if not self.auth_available:
            raise RuntimeError("Auth service not available")

        try:
            response = self._client.auth.sign_in_with_password({
                "email": email,
                "password": password
            })
            
            if response.user and response.session:
                logger.info(f"User signed in successfully: {email}")
                return {
                    "success": True,
                    "user": response.user,
                    "session": response.session,
                    "access_token": response.session.access_token
                }
            else:
                return {"success": False, "error": "Invalid credentials"}

        except Exception as e:
            logger.error(f"Sign in failed for {email}: {e}")
            return {"success": False, "error": str(e)}

    async def sign_out(self) -> bool:
        """Sign out current user"""
        if not self.auth_available:
            return False

        try:
            self._client.auth.sign_out()
            logger.info("User signed out successfully")
            return True
        except Exception as e:
            logger.error(f"Sign out failed: {e}")
            return False

    async def get_current_user(self) -> Optional[SupabaseUser]:
        """Get current authenticated user"""
        if not self.auth_available:
            return None

        try:
            user = self._client.auth.get_user()
            return user.user if user else None
        except Exception as e:
            logger.error(f"Failed to get current user: {e}")
            return None

    async def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token and return user info"""
        if not self.auth_available:
            return None

        try:
            response = self._client.auth.get_user(token)
            return response.user.model_dump() if response.user else None
        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            return None

    # Database Methods (replaces IUserStore, IRoleStore)
    
    async def create_user_profile(self, user_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create user profile in database (after Supabase Auth signup)"""
        if not self.database_available or not self._service_client:
            raise RuntimeError("Database service or service client not available")

        try:
            response = self._service_client.table("user_profiles").insert(user_data).execute()
            
            if response.data:
                logger.info(f"User profile created: {user_data.get('email')}")
                return response.data[0]
            return None

        except Exception as e:
            logger.error(f"Failed to create user profile: {e}")
            return None

    async def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user profile by ID"""
        if not self.database_available:
            return None

        try:
            response = self._client.table("user_profiles").select("*").eq("id", user_id).single().execute()
            return response.data if response.data else None

        except Exception as e:
            logger.error(f"Failed to get user profile {user_id}: {e}")
            return None

    async def update_user_profile(self, user_id: str, updates: Dict[str, Any]) -> bool:
        """Update user profile"""
        if not self.database_available:
            return False

        try:
            response = self._client.table("user_profiles").update(updates).eq("id", user_id).execute()
            return bool(response.data)

        except Exception as e:
            logger.error(f"Failed to update user profile {user_id}: {e}")
            return False

    async def list_users(self, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """List all users (admin operation)"""
        if not self.database_available or not self._service_client:
            return []

        try:
            response = self._service_client.table("user_profiles").select("*").range(offset, offset + limit - 1).execute()
            return response.data or []

        except Exception as e:
            logger.error(f"Failed to list users: {e}")
            return []

    # Storage Methods
    
    async def upload_file(self, bucket: str, file_path: str, file_data: bytes) -> Optional[str]:
        """Upload file to Supabase Storage"""
        if not self.storage_available:
            return None

        try:
            response = self._client.storage.from_(bucket).upload(file_path, file_data)
            
            if response:
                # Get public URL
                public_url = self._client.storage.from_(bucket).get_public_url(file_path)
                logger.info(f"File uploaded successfully: {file_path}")
                return public_url
            return None

        except Exception as e:
            logger.error(f"Failed to upload file {file_path}: {e}")
            return None

    async def delete_file(self, bucket: str, file_path: str) -> bool:
        """Delete file from Supabase Storage"""
        if not self.storage_available:
            return False

        try:
            response = self._client.storage.from_(bucket).remove([file_path])
            return bool(response)

        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {e}")
            return False

    # Utility Methods
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check"""
        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "initialized": self._initialized,
            "services": {
                "auth": self.auth_available,
                "database": self.database_available,
                "storage": self.storage_available,
                "realtime": self.realtime_available
            },
            "project_url": self.settings.supabase_url,
            "features_enabled": {
                "auth": self.settings.enable_auth,
                "realtime": self.settings.enable_realtime,
                "storage": self.settings.enable_storage,
                "edge_functions": self.settings.enable_edge_functions
            }
        }

    async def dispose(self) -> None:
        """Clean up resources"""
        if self._client:
            # Supabase client doesn't need explicit cleanup
            pass
        
        self._initialized = False
        logger.info("Unified Supabase client disposed")

    @property
    def is_available(self) -> bool:
        """Check if the client is available and initialized"""
        return self._initialized and (self.auth_available or self.database_available)

    @property
    def project_id(self) -> Optional[str]:
        """Get Supabase project ID"""
        if self.settings.supabase_url:
            try:
                return self.settings.supabase_url.split("//")[1].split(".")[0]
            except (IndexError, AttributeError):
                return None
        return None

    @property
    def dashboard_url(self) -> Optional[str]:
        """Get Supabase dashboard URL"""
        if self.project_id:
            return f"https://supabase.com/dashboard/project/{self.project_id}"
        return None
