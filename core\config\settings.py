"""
Advanced Configuration Management System
Implements environment-based configuration with validation, secrets, and structured settings
"""

import os
import secrets
from enum import Enum
from pathlib import Path
from typing import List, Optional, Dict, Any, Union, ClassVar
from pydantic import Field, validator, root_validator, HttpUrl, PostgresDsn, RedisDsn, SecretStr
from pydantic_settings import BaseSettings, SettingsConfigDict


class Environment(str, Enum):
    """Environment types"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


class LogLevel(str, Enum):
    """Logging levels"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class SecretManagementSettings(BaseSettings):
    """Secret management configuration"""

    # Secret provider configuration
    secret_provider: str = Field(default="cloudflare", description="Secret provider type (auto, vault, aws, azure, cloudflare, file)")
    secret_cache_ttl: int = Field(default=300, ge=60, le=3600, description="Secret cache TTL in seconds")
    fallback_to_env: bool = Field(default=True, description="Fallback to environment variables if secret provider fails")

    # HashiCorp Vault settings
    vault_url: Optional[str] = Field(default=None, description="Vault server URL")
    vault_token: Optional[SecretStr] = Field(default=None, description="Vault authentication token")
    vault_role_id: Optional[str] = Field(default=None, description="Vault AppRole role ID")
    vault_secret_id: Optional[SecretStr] = Field(default=None, description="Vault AppRole secret ID")
    vault_mount_point: str = Field(default="secret", description="Vault KV mount point")
    vault_kv_version: int = Field(default=2, ge=1, le=2, description="Vault KV engine version")

    # AWS Secrets Manager settings
    aws_region: str = Field(default="us-east-1", description="AWS region for Secrets Manager")
    aws_access_key_id: Optional[str] = Field(default=None, description="AWS access key ID")
    aws_secret_access_key: Optional[SecretStr] = Field(default=None, description="AWS secret access key")
    aws_session_token: Optional[SecretStr] = Field(default=None, description="AWS session token")

    # Azure Key Vault settings
    azure_key_vault_url: Optional[str] = Field(default=None, description="Azure Key Vault URL")

    # Cloudflare Secrets Store settings
    cloudflare_account_id: Optional[str] = Field(default=None, description="Cloudflare account ID")
    cloudflare_api_token: Optional[SecretStr] = Field(default=None, description="Cloudflare API token")
    cloudflare_store_id: Optional[str] = Field(default=None, description="Cloudflare Secrets Store ID")

    # File-based secrets settings
    secrets_file_path: str = Field(default="secrets.enc", description="Path to encrypted secrets file")
    secrets_encryption_key: Optional[SecretStr] = Field(default=None, description="Encryption key for file-based secrets")


class SecuritySettings(BaseSettings):
    """Security-related configuration"""

    # Core secrets
    secret_key: SecretStr = Field(
        default_factory=lambda: SecretStr(secrets.token_urlsafe(32)),
        description="Application secret key for cryptographic operations"
    )
    jwt_secret_key: SecretStr = Field(
        default_factory=lambda: SecretStr(secrets.token_urlsafe(32)),
        description="JWT signing secret key"
    )

    # API Security
    api_key_header: str = Field(default="X-API-Key", description="API key header name")
    jwt_algorithm: str = Field(default="HS256", description="JWT signing algorithm")
    access_token_expire_minutes: int = Field(default=30, ge=1, le=1440)
    refresh_token_expire_days: int = Field(default=7, ge=1, le=30)

    # Password policies
    password_min_length: int = Field(default=8, ge=6, le=128)
    password_require_special: bool = Field(default=True)
    password_require_numbers: bool = Field(default=True)
    password_require_uppercase: bool = Field(default=True)

    # Security headers
    enable_cors: bool = Field(default=True)
    cors_origins: List[str] = Field(default=["*"])
    cors_methods: List[str] = Field(default=["*"])
    cors_headers: List[str] = Field(default=["*"])

    # Trusted hosts
    trusted_hosts: List[str] = Field(default=["*"])

    @validator('cors_origins', pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v


class DatabaseSettings(BaseSettings):
    """Database configuration"""

    # Primary database
    database_url: Optional[PostgresDsn] = Field(
        default=None,
        description="PostgreSQL database URL"
    )
    database_pool_size: int = Field(default=20, ge=1, le=100)
    database_max_overflow: int = Field(default=0, ge=0, le=50)
    database_pool_timeout: int = Field(default=30, ge=1, le=300)
    database_pool_recycle: int = Field(default=3600, ge=300, le=86400)

    # Connection retry settings
    database_retry_attempts: int = Field(default=3, ge=1, le=10)
    database_retry_delay: float = Field(default=1.0, ge=0.1, le=60.0)

    # Migration settings
    auto_migrate: bool = Field(default=False)
    migration_location: str = Field(default="migrations")


class SupabaseSettings(BaseSettings):
    """Simple Supabase configuration - just the essentials"""

    # Core Supabase settings (optional)
    supabase_url: Optional[str] = Field(
        default=None,
        description="Supabase project URL (optional)",
        alias="SUPABASE_URL"
    )
    supabase_anon_key: Optional[str] = Field(
        default=None,
        description="Supabase anonymous/public key (optional)",
        alias="SUPABASE_ANON_KEY"
    )

    # Service role key for server-side operations
    supabase_service_role_key: Optional[str] = Field(
        default=None,
        description="Supabase service role key for server-side operations (keep secret!)",
        alias="SUPABASE_SERVICE_ROLE_KEY"
    )

    # Database settings (Supabase PostgreSQL)
    supabase_db_uri: Optional[str] = Field(
        default=None,
        description="Direct Supabase PostgreSQL connection URL (replaces regular DATABASE_URL)",
        alias="SUPABASE_DB_URI"
    )

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="allow"  # Allow extra fields for backward compatibility
    )


class CacheSettings(BaseSettings):
    """Cache configuration"""

    # Redis configuration
    redis_url: Optional[RedisDsn] = Field(
        default=None,
        description="Redis cache URL"
    )
    redis_pool_size: int = Field(default=10, ge=1, le=100)
    redis_socket_timeout: float = Field(default=30.0, ge=1.0, le=300.0)
    redis_socket_connect_timeout: float = Field(default=30.0, ge=1.0, le=300.0)

    # Cache settings
    default_cache_ttl: int = Field(default=3600, ge=1, le=86400)
    cache_key_prefix: str = Field(default="laneswap")

    # Local cache settings
    enable_local_cache: bool = Field(default=True)
    local_cache_size: int = Field(default=1000, ge=10, le=10000)
    local_cache_ttl: int = Field(default=300, ge=1, le=3600)


class MonitoringSettings(BaseSettings):
    """Monitoring and observability configuration"""

    # Metrics
    enable_metrics: bool = Field(default=True)
    metrics_port: int = Field(default=9090, ge=1024, le=65535)
    metrics_path: str = Field(default="/metrics")

    # Health checks
    health_check_interval: int = Field(default=30, ge=5, le=300)
    health_check_timeout: float = Field(default=10.0, ge=1.0, le=60.0)
    health_check_retries: int = Field(default=3, ge=1, le=10)

    # Tracing
    enable_tracing: bool = Field(default=False)
    jaeger_endpoint: Optional[HttpUrl] = Field(default=None)
    trace_sample_rate: float = Field(default=0.1, ge=0.0, le=1.0)

    # Alerting
    enable_alerting: bool = Field(default=False)
    alert_webhook_url: Optional[HttpUrl] = Field(default=None)

    # Performance monitoring
    enable_profiling: bool = Field(default=False)
    profile_sample_rate: float = Field(default=0.01, ge=0.0, le=1.0)


class ServiceSettings(BaseSettings):
    """Service discovery and communication configuration"""

    # Service registry
    service_discovery_url: Optional[HttpUrl] = Field(default=None)
    service_registry_refresh_interval: int = Field(default=60, ge=10, le=300)
    service_heartbeat_interval: int = Field(default=30, ge=5, le=120)

    # Circuit breaker
    circuit_breaker_failure_threshold: int = Field(default=5, ge=1, le=100)
    circuit_breaker_timeout: float = Field(default=60.0, ge=1.0, le=300.0)
    circuit_breaker_recovery_timeout: float = Field(default=30.0, ge=1.0, le=300.0)

    # Rate limiting
    default_rate_limit: int = Field(default=100, ge=1, le=10000)
    rate_limit_window: int = Field(default=60, ge=1, le=3600)

    # Timeout settings
    service_call_timeout: float = Field(default=30.0, ge=1.0, le=300.0)
    service_connect_timeout: float = Field(default=10.0, ge=1.0, le=60.0)

    # Retry settings
    service_retry_attempts: int = Field(default=3, ge=1, le=10)
    service_retry_backoff_factor: float = Field(default=2.0, ge=1.0, le=10.0)


class LoggingSettings(BaseSettings):
    """Logging configuration"""

    # Log levels
    log_level: LogLevel = Field(default=LogLevel.INFO)
    root_log_level: LogLevel = Field(default=LogLevel.WARNING)

    # Log formatting
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    log_date_format: str = Field(default="%Y-%m-%d %H:%M:%S")

    # Structured logging
    enable_json_logging: bool = Field(default=False)
    log_request_id: bool = Field(default=True)
    log_user_context: bool = Field(default=True)

    # Log destinations
    log_to_console: bool = Field(default=True)
    log_to_file: bool = Field(default=False)
    log_file_path: Optional[Path] = Field(default=None)
    log_file_max_size: int = Field(default=10485760, ge=1048576)  # 10MB
    log_file_backup_count: int = Field(default=5, ge=1, le=100)

    # External logging
    enable_sentry: bool = Field(default=False)
    sentry_dsn: Optional[str] = Field(default=None)
    sentry_environment: Optional[str] = Field(default=None)


class ApplicationSettings(BaseSettings):
    """Main application configuration"""

    # Basic app info
    app_name: str = Field(default="FastAPI Core Framework")
    app_version: str = Field(default="1.0.0")
    app_description: str = Field(default="Advanced FastAPI microservices framework")

    # Environment
    environment: Environment = Field(default=Environment.DEVELOPMENT)
    debug: bool = Field(default=False)

    # Server settings
    host: str = Field(default="0.0.0.0")
    port: int = Field(default=8000, ge=1024, le=65535)
    workers: int = Field(default=1, ge=1, le=32)

    # API settings
    api_prefix: str = Field(default="/api/v1")
    docs_url: Optional[str] = Field(default="/docs")
    redoc_url: Optional[str] = Field(default="/redoc")
    openapi_url: Optional[str] = Field(default="/openapi.json")

    # Feature flags
    enable_swagger: bool = Field(default=True)
    enable_admin_panel: bool = Field(default=False)
    enable_websockets: bool = Field(default=False)

    @validator('debug', pre=True)
    def set_debug_from_env(cls, v, values):
        if isinstance(v, str):
            return v.lower() in ('true', '1', 'yes', 'on')
        return v

    @validator('enable_swagger', pre=True)
    def disable_swagger_in_prod(cls, v, values):
        env = values.get('environment', Environment.DEVELOPMENT)
        if env == Environment.PRODUCTION:
            return False
        return v


class Settings(BaseSettings):
    """Comprehensive application settings with backward compatibility"""

    # Nested configuration sections
    app: ApplicationSettings = Field(default_factory=ApplicationSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    secrets: SecretManagementSettings = Field(default_factory=SecretManagementSettings)
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    supabase: SupabaseSettings = Field(default_factory=SupabaseSettings)
    cache: CacheSettings = Field(default_factory=CacheSettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    services: ServiceSettings = Field(default_factory=ServiceSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)

    # Additional configuration
    config_file: Optional[Path] = Field(default=None)

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_nested_delimiter="__",
        case_sensitive=False,
        extra="ignore",  # Ignore unknown fields for backward compatibility
        validate_assignment=True,
    )

    @root_validator(pre=True)
    def load_config_file(cls, values):
        """Load configuration from file if specified"""
        config_file = values.get('config_file')
        if config_file and Path(config_file).exists():
            try:
                import yaml
                with open(config_file, 'r') as f:
                    file_config = yaml.safe_load(f)
                    # Merge file config with values (values take precedence)
                    for key, value in file_config.items():
                        if key not in values:
                            values[key] = value
            except Exception:
                pass  # Ignore YAML loading errors
        return values

    @root_validator(pre=True)
    def map_legacy_config(cls, values):
        """Map legacy flat configuration to new hierarchical format"""
        # Map old flat keys to new nested structure
        legacy_mappings = {
            # Security mappings
            'secret_key': ('security', 'secret_key'),
            'jwt_secret_key': ('security', 'jwt_secret_key'),
            'core_api_key': ('security', 'api_key_header'),

            # Database mappings
            'database_url': ('database', 'database_url'),

            # Cache mappings
            'redis_url': ('cache', 'redis_url'),

            # Monitoring mappings
            'enable_metrics': ('monitoring', 'enable_metrics'),
            'metrics_port': ('monitoring', 'metrics_port'),

            # Service mappings
            'service_discovery_url': ('services', 'service_discovery_url'),

            # App mappings
            'app_name': ('app', 'app_name'),
            'app_version': ('app', 'app_version'),
            'environment': ('app', 'environment'),
            'debug': ('app', 'debug'),
            'host': ('app', 'host'),
            'port': ('app', 'port'),
        }

        for old_key, (section, new_key) in legacy_mappings.items():
            if old_key in values:
                # Create nested structure if it doesn't exist
                if section not in values:
                    values[section] = {}
                elif not isinstance(values[section], dict):
                    values[section] = {}

                # Only map if new key doesn't already exist
                if new_key not in values[section]:
                    values[section][new_key] = values[old_key]

        return values

    @validator('*', pre=True)
    def empty_str_to_none(cls, v):
        """Convert empty strings to None"""
        if isinstance(v, str) and v.strip() == '':
            return None
        return v

    # Convenience properties
    def is_production(self) -> bool:
        """Check if running in production"""
        return self.app.environment == Environment.PRODUCTION

    def is_development(self) -> bool:
        """Check if running in development"""
        return self.app.environment == Environment.DEVELOPMENT

    def is_testing(self) -> bool:
        """Check if running in testing"""
        return self.app.environment == Environment.TESTING

    def get_database_url(self) -> str:
        """Get database URL with fallback"""
        if self.database.database_url:
            return str(self.database.database_url)
        return "sqlite:///./test.db"

    def get_redis_url(self) -> str:
        """Get Redis URL with fallback"""
        if self.cache.redis_url:
            return str(self.cache.redis_url)
        return "redis://localhost:6379/0"

    def get_secret_key(self) -> str:
        """Get application secret key"""
        return self.security.secret_key.get_secret_value()

    def get_jwt_secret_key(self) -> str:
        """Get JWT secret key"""
        return self.security.jwt_secret_key.get_secret_value()

    # Convenience properties for backward compatibility
    @property
    def APP_NAME(self) -> str:
        return self.app.app_name

    @property
    def APP_VERSION(self) -> str:
        return self.app.app_version

    @property
    def ALLOWED_ORIGINS(self) -> List[str]:
        return self.security.cors_origins

    @property
    def ALLOWED_HOSTS(self) -> List[str]:
        return self.security.trusted_hosts

    @property
    def RATE_LIMIT(self) -> int:
        return self.services.default_rate_limit


def create_settings(env_file: Optional[str] = None) -> Settings:
    """Create application settings"""
    return Settings()


# Global settings instance - delay creation to avoid import-time errors
_settings: Optional[Settings] = None

def get_settings() -> Settings:
    """Get or create the global settings instance"""
    global _settings
    if _settings is None:
        _settings = create_settings()
    return _settings

# For backward compatibility
settings = get_settings()