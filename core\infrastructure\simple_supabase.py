"""
Simple Supabase Integration

This module provides a minimal Supabase integration focused on:
- Database connection (PostgreSQL)
- Basic user storage
- Optional dashboard access

Keeps it simple - no complex features, just what you need for development.
"""

import logging
from typing import Optional, Dict, Any
from datetime import datetime, timezone

from core.config.settings import SupabaseSettings

logger = logging.getLogger(__name__)


class SimpleSupabaseClient:
    """
    Simple Supabase client for basic database operations

    Features:
    - PostgreSQL database connection
    - Basic user management
    - Dashboard access (optional)
    - Minimal setup required
    """

    def __init__(self, settings: SupabaseSettings):
        self.settings = settings
        self._initialized = False
        self._supabase_client = None

    async def initialize(self) -> None:
        """Initialize simple Supabase connection"""
        if self._initialized:
            return

        # Check if we have Supabase credentials
        if not self.settings.supabase_url or not self.settings.supabase_anon_key:
            logger.info("Supabase credentials not provided - using regular PostgreSQL")
            self._initialized = True
            return

        try:
            # Only import supabase if we have credentials
            from supabase import create_client

            self._supabase_client = create_client(
                self.settings.supabase_url,
                self.settings.supabase_anon_key
            )

            self._initialized = True
            logger.info("Simple Supabase client initialized")

        except ImportError:
            logger.warning("Supabase package not installed - using regular PostgreSQL")
            self._initialized = True
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            logger.info("Falling back to regular PostgreSQL")
            self._initialized = True

    def get_database_url(self) -> Optional[str]:
        """Get the database URL for SQLAlchemy"""
        if self.settings.supabase_db_uri:
            return self.settings.supabase_db_uri
        return None

    def get_dashboard_url(self) -> Optional[str]:
        """Get Supabase dashboard URL"""
        if self.settings.supabase_url:
            # Extract project ID from URL
            project_id = self.settings.supabase_url.split("//")[1].split(".")[0]
            return f"https://supabase.com/dashboard/project/{project_id}"
        return None

    async def health_check(self) -> Dict[str, Any]:
        """Simple health check"""
        health_status = {
            "initialized": self._initialized,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "supabase_available": self._supabase_client is not None,
            "dashboard_url": self.get_dashboard_url()
        }

        if self._supabase_client:
            try:
                # Simple ping test
                result = self._supabase_client.table("_health_check").select("*").limit(1).execute()
                health_status["database_status"] = "healthy"
            except Exception as e:
                health_status["database_status"] = "unhealthy"
                health_status["database_error"] = str(e)
        else:
            health_status["database_status"] = "using_regular_postgresql"

        return health_status

    async def dispose(self) -> None:
        """Clean up resources"""
        self._initialized = False
        self._supabase_client = None
        logger.info("Simple Supabase client disposed")

    @property
    def client(self):
        """Get Supabase client (if available)"""
        return self._supabase_client

    @property
    def is_available(self) -> bool:
        """Check if Supabase is available"""
        return self._supabase_client is not None


def create_simple_supabase_client(settings: SupabaseSettings) -> SimpleSupabaseClient:
    """Factory function to create simple Supabase client"""
    return SimpleSupabaseClient(settings)


# Helper function to get database URL for your existing SQLAlchemy setup
def get_database_url_for_sqlalchemy(settings: SupabaseSettings, fallback_url: Optional[str] = None) -> str:
    """
    Get database URL for SQLAlchemy - Supabase if available, fallback otherwise

    This allows you to use Supabase PostgreSQL with your existing SQLAlchemy code
    without any changes to your models or queries.
    """

    # Try Supabase database URL first
    if settings.supabase_db_uri:
        logger.info("Using Supabase PostgreSQL database")
        return settings.supabase_db_uri

    # Fallback to regular database URL
    if fallback_url:
        logger.info("Using regular PostgreSQL database")
        return fallback_url

    # Default local PostgreSQL
    default_url = "postgresql+asyncpg://postgres:password@localhost:5432/laneswap_dev"
    logger.info("Using default local PostgreSQL database")
    return default_url


# Simple setup instructions
SETUP_INSTRUCTIONS = """
🚀 Simple Supabase Setup Instructions

1. **Create Supabase Project:**
   - Go to https://supabase.com
   - Create a new project
   - Wait for it to initialize (2-3 minutes)

2. **Get Your Credentials:**
   - Go to Settings > API
   - Copy your Project URL
   - Copy your anon/public key
   - Copy your Database URL (from Settings > Database)

3. **Add to Your .env File:**
   ```
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_ANON_KEY=your-anon-key
   SUPABASE_DB_URL=postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres
   ```

4. **That's It!**
   - Your existing SQLAlchemy code will work unchanged
   - You get a beautiful dashboard at https://supabase.com/dashboard
   - Automatic backups and scaling
   - No code changes needed

5. **Optional: Install Supabase Package**
   ```bash
   pip install supabase
   ```
   (Only needed if you want dashboard access from code)

Benefits:
✅ Keep your existing code unchanged
✅ Get a visual database dashboard
✅ Automatic backups and scaling
✅ Better performance than local PostgreSQL
✅ Easy team collaboration
✅ Production-ready hosting
"""


def print_setup_instructions():
    """Print setup instructions"""
    print(SETUP_INSTRUCTIONS)


if __name__ == "__main__":
    print_setup_instructions()
