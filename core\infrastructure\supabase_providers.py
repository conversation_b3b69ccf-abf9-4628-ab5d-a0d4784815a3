"""
Supabase-Native Domain Interface Implementations

This module provides Supabase-native implementations of domain interfaces,
replacing custom JWT auth and database stores with Supabase's built-in services.
"""

import logging
from typing import Any, Dict, List, Optional, Set
from datetime import datetime, timezone

from core.domain.interfaces import ISecurityProvider, IUserStore, IRoleStore
from core.domain.models import User, Role, UserStatus, AuthToken
from core.infrastructure.unified_supabase import UnifiedSupabaseClient

logger = logging.getLogger(__name__)


class SupabaseSecurityProvider(ISecurityProvider):
    """
    Supabase-native security provider using Supabase Auth

    Replaces JWT-based authentication with Supabase's built-in auth system.
    """

    def __init__(self, supabase_client: UnifiedSupabaseClient):
        self.supabase = supabase_client

    async def authenticate_user(self, username: str, password: str) -> Optional[AuthToken]:
        """Authenticate user with Supabase Auth"""
        try:
            # Supabase uses email for authentication
            result = await self.supabase.sign_in(username, password)

            if result["success"] and result.get("session"):
                session = result["session"]
                user_data = result["user"]

                return AuthToken(
                    access_token=session.access_token,
                    refresh_token=session.refresh_token,
                    token_type="bearer",
                    expires_in=session.expires_in,
                    user_id=user_data.id,
                    username=user_data.email,
                    roles=[]  # Will be populated from user profile
                )

            return None

        except Exception as e:
            logger.error(f"Authentication failed for {username}: {e}")
            return None

    async def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token using Supabase Auth"""
        try:
            user_data = await self.supabase.verify_token(token)

            if user_data:
                # Get additional user profile data
                profile = await self.supabase.get_user_profile(user_data["id"])

                return {
                    "user_id": user_data["id"],
                    "username": user_data["email"],
                    "email": user_data["email"],
                    "roles": profile.get("roles", []) if profile else [],
                    "is_superuser": profile.get("is_superuser", False) if profile else False,
                    "metadata": user_data.get("user_metadata", {})
                }

            return None

        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            return None

    async def refresh_token(self, refresh_token: str) -> Optional[AuthToken]:
        """Refresh access token using Supabase Auth"""
        try:
            # Supabase handles token refresh automatically
            # This is a placeholder for manual refresh if needed
            logger.info("Token refresh requested - Supabase handles this automatically")
            return None

        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            return None

    async def revoke_token(self, token: str) -> bool:
        """Revoke token by signing out"""
        try:
            return await self.supabase.sign_out()

        except Exception as e:
            logger.error(f"Token revocation failed: {e}")
            return False

    async def create_user(self, username: str, password: str, email: str, **kwargs) -> Optional[User]:
        """Create new user with Supabase Auth"""
        try:
            # Sign up user with Supabase Auth
            result = await self.supabase.sign_up(
                email=email,
                password=password,
                user_metadata={
                    "username": username,
                    "first_name": kwargs.get("first_name"),
                    "last_name": kwargs.get("last_name")
                }
            )

            if result["success"] and result.get("user"):
                auth_user = result["user"]

                # Create user profile in database
                profile_data = {
                    "id": auth_user.id,
                    "auth_user_id": auth_user.id,
                    "username": username,
                    "email": email,
                    "first_name": kwargs.get("first_name"),
                    "last_name": kwargs.get("last_name"),
                    "status": kwargs.get("status", UserStatus.ACTIVE.value),
                    "is_superuser": kwargs.get("is_superuser", False),
                    "is_verified": auth_user.email_confirmed_at is not None,
                    "created_at": datetime.now(timezone.utc).isoformat(),
                    "updated_at": datetime.now(timezone.utc).isoformat(),
                    "metadata": kwargs.get("metadata", {})
                }

                profile = await self.supabase.create_user_profile(profile_data)

                if profile:
                    return User(
                        id=profile["id"],
                        username=profile["username"],
                        email=profile["email"],
                        first_name=profile.get("first_name"),
                        last_name=profile.get("last_name"),
                        status=UserStatus(profile["status"]),
                        is_superuser=profile["is_superuser"],
                        is_verified=profile["is_verified"],
                        created_at=datetime.fromisoformat(profile["created_at"].replace("Z", "+00:00")),
                        updated_at=datetime.fromisoformat(profile["updated_at"].replace("Z", "+00:00")),
                        role_ids=set(profile.get("metadata", {}).get("role_ids", []))
                    )

            return None

        except Exception as e:
            logger.error(f"User creation failed for {username}: {e}")
            return None

    async def get_user_permissions(self, user_id: str) -> List[str]:
        """Get user permissions from Supabase database"""
        try:
            profile = await self.supabase.get_user_profile(user_id)

            if profile:
                # If superuser, return all permissions
                if profile.get("is_superuser", False):
                    return ["*"]

                # Get permissions from roles (would need role-permission mapping)
                # This is a simplified implementation
                roles = profile.get("metadata", {}).get("role_ids", [])
                permissions = []

                # Map roles to permissions (this would be in a separate table)
                role_permission_map = {
                    "admin": ["users:read", "users:write", "users:delete"],
                    "user": ["users:read"],
                    "moderator": ["users:read", "users:write"]
                }

                for role in roles:
                    permissions.extend(role_permission_map.get(role, []))

                return list(set(permissions))  # Remove duplicates

            return []

        except Exception as e:
            logger.error(f"Failed to get permissions for user {user_id}: {e}")
            return []

    # Additional required methods for ISecurityProvider

    async def authenticate(self, credentials: Dict[str, Any]) -> Optional[AuthToken]:
        """Authenticate with credentials (alias for authenticate_user)"""
        username = credentials.get("username") or credentials.get("email")
        password = credentials.get("password")

        if username and password:
            return await self.authenticate_user(username, password)
        return None

    async def authorize(self, token: str, required_permissions: List[str]) -> bool:
        """Authorize user based on token and required permissions"""
        try:
            user_data = await self.verify_token(token)

            if not user_data:
                return False

            user_permissions = await self.get_user_permissions(user_data["user_id"])

            # If user has wildcard permission, allow everything
            if "*" in user_permissions:
                return True

            # Check if user has all required permissions
            return all(perm in user_permissions for perm in required_permissions)

        except Exception as e:
            logger.error(f"Authorization failed: {e}")
            return False

    async def get_current_user_id(self, token: str) -> Optional[str]:
        """Get current user ID from token"""
        user_data = await self.verify_token(token)
        return user_data.get("user_id") if user_data else None

    async def change_password(self, user_id: str, old_password: str, new_password: str) -> bool:
        """Change user password (Supabase handles this through auth)"""
        # This would typically be handled through Supabase Auth's password change flow
        logger.info("Password change should be handled through Supabase Auth UI")
        return False

    async def reset_password(self, email: str) -> bool:
        """Reset user password (Supabase handles this through auth)"""
        # This would typically be handled through Supabase Auth's password reset flow
        logger.info("Password reset should be handled through Supabase Auth UI")
        return False


class SupabaseUserStore(IUserStore):
    """
    Supabase-native user store using Supabase database with RLS
    """

    def __init__(self, supabase_client: UnifiedSupabaseClient):
        self.supabase = supabase_client

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID from Supabase"""
        try:
            profile = await self.supabase.get_user_profile(user_id)

            if profile:
                return User(
                    id=profile["id"],
                    username=profile["username"],
                    email=profile["email"],
                    first_name=profile.get("first_name"),
                    last_name=profile.get("last_name"),
                    status=UserStatus(profile["status"]),
                    is_superuser=profile["is_superuser"],
                    is_verified=profile["is_verified"],
                    created_at=datetime.fromisoformat(profile["created_at"].replace("Z", "+00:00")),
                    updated_at=datetime.fromisoformat(profile["updated_at"].replace("Z", "+00:00")),
                    role_ids=set(profile.get("metadata", {}).get("role_ids", []))
                )

            return None

        except Exception as e:
            logger.error(f"Failed to get user {user_id}: {e}")
            return None

    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username from Supabase"""
        try:
            response = self.supabase._client.table("user_profiles").select("*").eq("username", username).single().execute()

            if response.data:
                profile = response.data
                return User(
                    id=profile["id"],
                    username=profile["username"],
                    email=profile["email"],
                    first_name=profile.get("first_name"),
                    last_name=profile.get("last_name"),
                    status=UserStatus(profile["status"]),
                    is_superuser=profile["is_superuser"],
                    is_verified=profile["is_verified"],
                    created_at=datetime.fromisoformat(profile["created_at"].replace("Z", "+00:00")),
                    updated_at=datetime.fromisoformat(profile["updated_at"].replace("Z", "+00:00")),
                    role_ids=set(profile.get("metadata", {}).get("role_ids", []))
                )

            return None

        except Exception as e:
            logger.error(f"Failed to get user by username {username}: {e}")
            return None

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email from Supabase"""
        try:
            response = self.supabase._client.table("user_profiles").select("*").eq("email", email).single().execute()

            if response.data:
                profile = response.data
                return User(
                    id=profile["id"],
                    username=profile["username"],
                    email=profile["email"],
                    first_name=profile.get("first_name"),
                    last_name=profile.get("last_name"),
                    status=UserStatus(profile["status"]),
                    is_superuser=profile["is_superuser"],
                    is_verified=profile["is_verified"],
                    created_at=datetime.fromisoformat(profile["created_at"].replace("Z", "+00:00")),
                    updated_at=datetime.fromisoformat(profile["updated_at"].replace("Z", "+00:00")),
                    role_ids=set(profile.get("metadata", {}).get("role_ids", []))
                )

            return None

        except Exception as e:
            logger.error(f"Failed to get user by email {email}: {e}")
            return None

    async def create_user(self, user_data: Dict[str, Any]) -> User:
        """Create user (this should go through SupabaseSecurityProvider)"""
        raise NotImplementedError("Use SupabaseSecurityProvider.create_user() instead")

    async def update_user(self, user_id: str, user_data: Dict[str, Any]) -> Optional[User]:
        """Update user profile in Supabase"""
        try:
            success = await self.supabase.update_user_profile(user_id, user_data)

            if success:
                return await self.get_user_by_id(user_id)

            return None

        except Exception as e:
            logger.error(f"Failed to update user {user_id}: {e}")
            return None

    async def delete_user(self, user_id: str) -> bool:
        """Soft delete user by updating status"""
        try:
            return await self.supabase.update_user_profile(user_id, {
                "status": UserStatus.INACTIVE.value,
                "updated_at": datetime.now(timezone.utc).isoformat()
            })

        except Exception as e:
            logger.error(f"Failed to delete user {user_id}: {e}")
            return False

    async def list_users(self, limit: int = 50, offset: int = 0) -> List[User]:
        """List users from Supabase"""
        try:
            profiles = await self.supabase.list_users(limit, offset)

            users = []
            for profile in profiles:
                user = User(
                    id=profile["id"],
                    username=profile["username"],
                    email=profile["email"],
                    first_name=profile.get("first_name"),
                    last_name=profile.get("last_name"),
                    status=UserStatus(profile["status"]),
                    is_superuser=profile["is_superuser"],
                    is_verified=profile["is_verified"],
                    created_at=datetime.fromisoformat(profile["created_at"].replace("Z", "+00:00")),
                    updated_at=datetime.fromisoformat(profile["updated_at"].replace("Z", "+00:00")),
                    role_ids=set(profile.get("metadata", {}).get("role_ids", []))
                )
                users.append(user)

            return users

        except Exception as e:
            logger.error(f"Failed to list users: {e}")
            return []


class SupabaseRoleStore(IRoleStore):
    """
    Simplified role store for Supabase

    In a full Supabase implementation, roles would be managed through
    Supabase's built-in role system or a custom roles table.
    """

    def __init__(self, supabase_client: UnifiedSupabaseClient):
        self.supabase = supabase_client

    async def get_role_by_id(self, role_id: str) -> Optional[Role]:
        """Get role by ID (simplified implementation)"""
        # This would query a roles table in a full implementation
        return None

    async def get_role_by_name(self, name: str) -> Optional[Role]:
        """Get role by name (simplified implementation)"""
        # This would query a roles table in a full implementation
        return None

    async def create_role(self, role_data: Dict[str, Any]) -> Role:
        """Create role (simplified implementation)"""
        # This would create a role in a roles table
        raise NotImplementedError("Role management not implemented in simplified version")

    async def update_role(self, role_id: str, role_data: Dict[str, Any]) -> Optional[Role]:
        """Update role (simplified implementation)"""
        return None

    async def delete_role(self, role_id: str) -> bool:
        """Delete role (simplified implementation)"""
        return False

    async def list_roles(self, limit: int = 50, offset: int = 0) -> List[Role]:
        """List roles (simplified implementation)"""
        return []

    async def get_user_roles(self, user_id: str) -> List[Role]:
        """Get user roles (simplified implementation)"""
        return []

    async def assign_role_to_user(self, user_id: str, role_id: str) -> bool:
        """Assign role to user (simplified implementation)"""
        return False

    async def remove_role_from_user(self, user_id: str, role_id: str) -> bool:
        """Remove role from user (simplified implementation)"""
        return False

    async def get_role_permissions(self, role_id: str) -> List[str]:
        """Get role permissions (simplified implementation)"""
        return []
